<?php

class TracesNtChedClient {
    private $username;
    private $authKey;
    private $clientId;
    private $useProduction;

    public function __construct($username, $authKey, $clientId, $useProduction = false) {
        $this->username = $username;
        $this->authKey = trim($authKey); 
        $this->clientId = $clientId;
        $this->useProduction = $useProduction;
    }

    public function getValidatedFishCertificates($startDate, $endDate) {
        // Fixed endpoints based on the helpdesk response and your image
        $endpoint = $this->useProduction
            ? 'https://webgate.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2'
            : 'https://webgate.training.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2';

        $soapRequest = $this->createSoapRequest(
            '<ched:FindChedCertificateRequest>
            <ched:pageSize>50</ched:pageSize>
<ched:offset>0</ched:offset>
                <ched:Type>P</ched:Type>
                <ched:Status>70</ched:Status>
                <ched:CountryOfIssuance>MR</ched:CountryOfIssuance>
                <ched:UpdateDateTimeRange>
                    <base:From>' . $startDate . 'T00:00:00Z</base:From>
                    <base:To>' . $endDate . 'T23:59:59Z</base:To>
                </ched:UpdateDateTimeRange>
            </ched:FindChedCertificateRequest>'
        );

        $response = $this->sendSoapRequest($endpoint, $soapRequest);
        return $this->parseResponse($response);
    }

    private function createSoapRequest($body) {
        date_default_timezone_set('UTC');

        $nonceRaw = random_bytes(16);
        $nonce = base64_encode($nonceRaw);

        $now = new DateTime('now', new DateTimeZone('UTC'));

        // Use the same timestamp format for both digest calculation and timestamp elements (Method 2)
        $created = $now->format('Y-m-d\TH:i:s\Z');
        $expires = (clone $now)->modify('+5 minutes')->format('Y-m-d\TH:i:s\Z');

        $passwordDigest = base64_encode(
            sha1($nonceRaw . $created . $this->authKey, true)
        );

        return '<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope
    xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
    xmlns:base="http://ec.europa.eu/sanco/tracesnt/base/v4"
    xmlns:cert="http://ec.europa.eu/tracesnt/certificate/base/v01"
    xmlns:ched="http://ec.europa.eu/tracesnt/certificate/ched/v2">
    <soapenv:Header>
        <wsse:Security
            xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"
            xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
            soapenv:mustUnderstand="1">
            <wsse:UsernameToken wsu:Id="UsernameToken-' . uniqid() . '">
                <wsse:Username>' . htmlspecialchars($this->username) . '</wsse:Username>
                <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest">' . $passwordDigest . '</wsse:Password>
                <wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">' . $nonce . '</wsse:Nonce>
                <wsu:Created>' . $created . '</wsu:Created>
            </wsse:UsernameToken>
            <wsu:Timestamp wsu:Id="TS-' . uniqid() . '">
                <wsu:Created>' . $created . '</wsu:Created>
                <wsu:Expires>' . $expires . '</wsu:Expires>
            </wsu:Timestamp>
        </wsse:Security>
        <base:LanguageCode>en</base:LanguageCode>
        <base:WebServiceClientId>' . htmlspecialchars($this->clientId) . '</base:WebServiceClientId>
    </soapenv:Header>
    <soapenv:Body>' . $body . '</soapenv:Body>
</soapenv:Envelope>';
    }

    private function sendSoapRequest($endpoint, $soapRequest) {
        $headers = [
            'Content-Type: text/xml; charset=utf-8',
            'SOAPAction: "findChedCertificate"',
            'Content-Length: ' . strlen($soapRequest)
        ];

        file_put_contents('request.xml', $soapRequest);

        $ch = curl_init($endpoint);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $soapRequest);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_VERBOSE, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $verbose = fopen('php://temp', 'w+');
        curl_setopt($ch, CURLOPT_STDERR, $verbose);

        $response = curl_exec($ch);

        if (curl_errno($ch)) {
            rewind($verbose);
            $verboseLog = stream_get_contents($verbose);
            fclose($verbose);
            throw new Exception('Curl error: ' . curl_error($ch) . "\nVerbose log:\n" . $verboseLog);
        }

        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        file_put_contents('response.xml', $response);


        
        if ($httpCode != 200) {
            throw new Exception('HTTP error: ' . $httpCode . "\nResponse:\n" . $response);
        }

        return $response;
    }

    private function parseResponse($xmlResponse) {
        file_put_contents('raw_response.xml', $xmlResponse);

        $dom = new DOMDocument();
        libxml_use_internal_errors(true);

        if (!$dom->loadXML($xmlResponse)) {
            $errors = libxml_get_errors();
            $errorMessages = array_map(fn($e) => $e->message, $errors);
            throw new Exception("Failed to parse XML response:\n" . implode("\n", $errorMessages));
        }

        $xpath = new DOMXPath($dom);
        $xpath->registerNamespace('soap', 'http://schemas.xmlsoap.org/soap/envelope/');
        $xpath->registerNamespace('ched', 'http://ec.europa.eu/tracesnt/certificate/ched/v2');
        $xpath->registerNamespace('cert', 'http://ec.europa.eu/tracesnt/certificate/base/v01');
        $xpath->registerNamespace('base', 'http://ec.europa.eu/sanco/tracesnt/base/v4');

        $fault = $xpath->query('//soap:Fault');
        if ($fault->length > 0) {
            $faultCode = $xpath->query('//soap:faultcode', $fault->item(0))->item(0)?->nodeValue ?? 'Unknown';
            $faultString = $xpath->query('//soap:faultstring', $fault->item(0))->item(0)?->nodeValue ?? 'Unknown';
            throw new Exception("SOAP Fault: [$faultCode] $faultString");
        }

        $certificates = [];
        $certNodes = $xpath->query('//ched:certificate');

        if ($certNodes->length === 0) {
            return [];
        }

        foreach ($certNodes as $cert) {
            $certificates[] = [
                'id' => $xpath->query('./cert:id', $cert)->item(0)?->nodeValue,
                'type' => $xpath->query('./cert:type', $cert)->item(0)?->nodeValue,
                'status' => $xpath->query('./cert:status', $cert)->item(0)?->nodeValue,
                'validation_date' => $xpath->query('./cert:decisionDateTime', $cert)->item(0)?->nodeValue,
                'exporter' => $xpath->query('./cert:consignorName', $cert)->item(0)?->nodeValue,
                'country_of_origin' => $xpath->query('./cert:countryOfOriginName', $cert)->item(0)?->nodeValue,
            ];
        }

        return $certificates;
    }

    public function debugAuthentication() {
        date_default_timezone_set('UTC');
        $nonceRaw = random_bytes(16);
        $nonce = base64_encode($nonceRaw);

        $now = new DateTime('now', new DateTimeZone('UTC'));
        $createdForDigestAndUsernameToken = $now->format('Y-m-d\TH:i:s\Z');
        $createdForTimestampElement = $now->format('Y-m-d\TH:i:s.v\Z');
        $expiresForTimestampElement = (clone $now)->modify('+5 minutes')->format('Y-m-d\TH:i:s.v\Z');

        echo "Debug Authentication:\n";
        echo "Username: " . $this->username . "\n";
        echo "Client ID: " . $this->clientId . "\n";
        echo "Auth Key (first 10 chars): " . substr($this->authKey, 0, 10) . "...\n";
        echo "Auth Key Length: " . strlen($this->authKey) . "\n";
        echo "Nonce (base64): " . $nonce . "\n";
        echo "Created (UsernameToken/Digest - no ms): " . $createdForDigestAndUsernameToken . "\n";
        echo "Created (Timestamp element - with ms): " . $createdForTimestampElement . "\n";
        echo "Expires (Timestamp element - with ms): " . $expiresForTimestampElement . "\n";

        $passwordDigest = base64_encode(
            sha1($nonceRaw . $createdForDigestAndUsernameToken . $this->authKey, true)
        );
        echo "Password Digest: " . $passwordDigest . "\n";

        // Show which endpoint will be used
        $endpoint = $this->useProduction
            ? 'https://webgate.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2'
            : 'https://webgate.training.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2';
        echo "Endpoint: " . $endpoint . "\n";
    }

    public function testAuthenticationMethods() {
        // Test different endpoints
        $endpoints = [
            'training' => 'https://webgate.training.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2',
            'acceptance' => 'https://webgate.acceptance.ec.europa.eu/tracesnt/ws/ChedCertificateServiceV2',
            'production' => 'https://webgate.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2'
        ];

        // Test different auth keys
        $authKeys = [
            'current' => $this->authKey,
            'alternative' => '0q2JpCSjeGEMUR1qQPXLg15SYiEPi9r6rntcgcXa'
        ];

        // Test different digest calculation methods
        $methods = [
            1 => 'Raw nonce + created + auth_key (current method)',
            2 => 'Raw nonce + created + auth_key (same timestamp format)',
            3 => 'Raw nonce + created + base64_decoded_auth_key',
            4 => 'Base64 nonce + created + auth_key'
        ];

        foreach ($endpoints as $endpointName => $endpoint) {
            echo "\n=== TESTING ENDPOINT: $endpointName ($endpoint) ===\n";

            foreach ($authKeys as $keyName => $authKey) {
                echo "\n--- Testing with $keyName auth key ---\n";

                // Temporarily set the auth key for testing
                $originalAuthKey = $this->authKey;
                $this->authKey = $authKey;

                foreach ($methods as $methodNum => $description) {
                    echo "\nMethod $methodNum ($description):\n";

                    try {
                        $soapRequest = $this->createSoapRequestMethod($methodNum);
                        $response = $this->sendTestRequest($endpoint, $soapRequest, "{$endpointName}_{$keyName}_{$methodNum}");

                        if (strpos($response, 'UnauthenticatedException') === false) {
                            echo "✓ SUCCESS! Endpoint: $endpointName, Auth Key: $keyName, Method: $methodNum\n";
                            echo "Response preview: " . substr($response, 0, 200) . "...\n";
                            file_put_contents("successful_{$endpointName}_{$keyName}_{$methodNum}_response.xml", $response);

                            // Restore original auth key
                            $this->authKey = $originalAuthKey;
                            return ['endpoint' => $endpointName, 'authKey' => $keyName, 'method' => $methodNum];
                        } else {
                            echo "✗ Failed - UnauthenticatedException\n";
                        }

                    } catch (Exception $e) {
                        echo "✗ Failed with exception: " . $e->getMessage() . "\n";
                    }
                }

                // Restore original auth key
                $this->authKey = $originalAuthKey;
            }
        }

        return false; // No combination worked
    }

    private function createSoapRequestMethod($method) {
        date_default_timezone_set('UTC');
        $nonceRaw = random_bytes(16);
        $nonce = base64_encode($nonceRaw);

        $now = new DateTime('now', new DateTimeZone('UTC'));

        // Calculate password digest based on method
        switch ($method) {
            case 1: // Current method - Raw nonce + created + auth_key (different timestamp formats)
                $createdForDigest = $now->format('Y-m-d\TH:i:s\Z');
                $createdForTimestamp = $now->format('Y-m-d\TH:i:s.v\Z');
                $expiresForTimestamp = (clone $now)->modify('+5 minutes')->format('Y-m-d\TH:i:s.v\Z');
                $passwordDigest = base64_encode(
                    sha1($nonceRaw . $createdForDigest . $this->authKey, true)
                );
                break;

            case 2: // Same timestamp format for both
                $createdForDigest = $now->format('Y-m-d\TH:i:s\Z');
                $createdForTimestamp = $createdForDigest;
                $expiresForTimestamp = (clone $now)->modify('+5 minutes')->format('Y-m-d\TH:i:s\Z');
                $passwordDigest = base64_encode(
                    sha1($nonceRaw . $createdForDigest . $this->authKey, true)
                );
                break;

            case 3: // Raw nonce + created + base64_decoded_auth_key
                $createdForDigest = $now->format('Y-m-d\TH:i:s\Z');
                $createdForTimestamp = $createdForDigest;
                $expiresForTimestamp = (clone $now)->modify('+5 minutes')->format('Y-m-d\TH:i:s\Z');
                $passwordDigest = base64_encode(
                    sha1($nonceRaw . $createdForDigest . base64_decode($this->authKey), true)
                );
                break;

            case 4: // Base64 nonce + created + auth_key
                $createdForDigest = $now->format('Y-m-d\TH:i:s\Z');
                $createdForTimestamp = $createdForDigest;
                $expiresForTimestamp = (clone $now)->modify('+5 minutes')->format('Y-m-d\TH:i:s\Z');
                $passwordDigest = base64_encode(
                    sha1($nonce . $createdForDigest . $this->authKey, true)
                );
                break;

            default:
                throw new Exception("Unknown method: $method");
        }

        echo "Method $method - Created for digest: $createdForDigest\n";
        echo "Method $method - Password digest: $passwordDigest\n";

        return '<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope
    xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
    xmlns:base="http://ec.europa.eu/sanco/tracesnt/base/v4"
    xmlns:cert="http://ec.europa.eu/tracesnt/certificate/base/v01"
    xmlns:ched="http://ec.europa.eu/tracesnt/certificate/ched/v2">
    <soapenv:Header>
        <wsse:Security
            xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"
            xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
            soapenv:mustUnderstand="1">
            <wsse:UsernameToken wsu:Id="UsernameToken-' . uniqid() . '">
                <wsse:Username>' . htmlspecialchars($this->username) . '</wsse:Username>
                <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest">' . $passwordDigest . '</wsse:Password>
                <wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">' . $nonce . '</wsse:Nonce>
                <wsu:Created>' . $createdForDigest . '</wsu:Created>
            </wsse:UsernameToken>
            <wsu:Timestamp wsu:Id="TS-' . uniqid() . '">
                <wsu:Created>' . $createdForTimestamp . '</wsu:Created>
                <wsu:Expires>' . $expiresForTimestamp . '</wsu:Expires>
            </wsu:Timestamp>
        </wsse:Security>
        <base:LanguageCode>en</base:LanguageCode>
        <base:WebServiceClientId>' . htmlspecialchars($this->clientId) . '</base:WebServiceClientId>
    </soapenv:Header>
    <soapenv:Body>
        <ched:FindChedCertificateRequest>
            <ched:Type>P</ched:Type>
            <ched:Status>70</ched:Status>
            <ched:CountryOfIssuance>MR</ched:CountryOfIssuance>
            <ched:UpdateDateTimeRange>
                <base:From>2024-01-01T00:00:00Z</base:From>
                <base:To>2024-06-20T23:59:59Z</base:To>
            </ched:UpdateDateTimeRange>
        </ched:FindChedCertificateRequest>
    </soapenv:Body>
</soapenv:Envelope>';
    }

    private function sendTestRequest($endpoint, $soapRequest, $methodNum) {
        $headers = [
            'Content-Type: text/xml; charset=utf-8',
            'SOAPAction: "findChedCertificate"',
            'Content-Length: ' . strlen($soapRequest)
        ];

        file_put_contents("test_method_{$methodNum}_request.xml", $soapRequest);

        $ch = curl_init($endpoint);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $soapRequest);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);

        if (curl_errno($ch)) {
            throw new Exception('Curl error: ' . curl_error($ch));
        }

        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        file_put_contents("test_method_{$methodNum}_response.xml", $response);

        if ($httpCode != 200 && $httpCode != 500) {
            throw new Exception('HTTP error: ' . $httpCode);
        }

        return $response;
    }
}

try {

    $client = new TracesNtChedClient(
        'n00385tm',
        '7qzFRburdCKThQqGyoefyTN6xuDFIXVC3FUSDdFh',  // Working auth key
        'onispa-mr',
        true  
    );

    echo "=== TESTING WITH WORKING CONFIGURATION ===\n";
    echo "Using working auth key and Method 2 authentication...\n";

    // Test the actual certificate fetching
    echo "\n=== ATTEMPTING TO FETCH CERTIFICATES ===\n";
    $certificates = $client->getValidatedFishCertificates('2024-12-01', '2025-05-01');

    echo "SUCCESS! Validated Fish Export Certificates found: " . count($certificates) . "\n";
    if (!empty($certificates)) {
        echo "\nFirst certificate details:\n";
        print_r($certificates[0]);

        echo "\nAll certificates:\n";
        foreach ($certificates as $index => $cert) {
            echo "Certificate " . ($index + 1) . ":\n";
            echo "  ID: " . ($cert['id'] ?? 'N/A') . "\n";
            echo "  Type: " . ($cert['type'] ?? 'N/A') . "\n";
            echo "  Status: " . ($cert['status'] ?? 'N/A') . "\n";
            echo "  Validation Date: " . ($cert['validation_date'] ?? 'N/A') . "\n";
            echo "  Exporter: " . ($cert['exporter'] ?? 'N/A') . "\n";
            echo "  Country of Origin: " . ($cert['country_of_origin'] ?? 'N/A') . "\n";
            echo "\n";
        }
    } else {
        echo "No certificates found for the specified date range.\n";
    }

    // Optionally, you can still run the comprehensive test
    echo "\n=== OPTIONAL: COMPREHENSIVE AUTHENTICATION TEST ===\n";
    echo "Uncomment the lines below if you want to run the full test again:\n";
    /*
    $successfulConfig = $client->testAuthenticationMethods();

    if ($successfulConfig) {
        echo "\n🎉 SUCCESS! Found working configuration:\n";
        echo "Endpoint: " . $successfulConfig['endpoint'] . "\n";
        echo "Auth Key: " . $successfulConfig['authKey'] . "\n";
        echo "Method: " . $successfulConfig['method'] . "\n";
        echo "\nNow you need to:\n";
        echo "1. Update your auth key if using 'alternative'\n";
        echo "2. Update your endpoint URL based on the working endpoint\n";
        echo "3. Update your authentication method in the createSoapRequest function\n";

        // Show the working endpoint URL
        $workingEndpoints = [
            'training' => 'https://webgate.training.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2',
            'acceptance' => 'https://webgate.acceptance.ec.europa.eu/tracesnt/ws/ChedCertificateServiceV2',
            'production' => 'https://webgate.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2'
        ];
        echo "\nWorking endpoint URL: " . $workingEndpoints[$successfulConfig['endpoint']] . "\n";

    } else {
        echo "\n❌ None of the authentication methods worked.\n";
        echo "Please check:\n";
        echo "1. Your username is correct: n00385tm\n";
        echo "2. Your WebService authentication key is current and matches what's in your TRACES profile\n";
        echo "3. Your client ID matches what TRACES assigned: onispa-mr\n";
        echo "4. Your WebService access is still active\n";
        echo "5. You're using the correct endpoint (training vs production)\n";
        echo "6. Contact TRACES support to verify your credentials\n";
    }
    */

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";

    if (file_exists('request.xml')) echo "\nRequest XML saved to request.xml\n";
    if (file_exists('response.xml')) echo "Response XML saved to response.xml\n";
    if (file_exists('raw_response.xml')) echo "Raw response saved to raw_response.xml\n";
}